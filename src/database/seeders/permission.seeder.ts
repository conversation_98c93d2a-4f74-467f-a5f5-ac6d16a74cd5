import { DataSource } from 'typeorm';
import { Permission } from '@/entities/permission.entity';
import { PermissionsType } from '@/common/constants';

/**
 * Permission Seeder
 * Seeds the permissions table with predefined permissions
 */
export class PermissionSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const permissionRepository = dataSource.getRepository(Permission);

    // Check if permissions already exist
    const existingPermissions = await permissionRepository.count();
    if (existingPermissions > 0) {
      console.log('Permissions already exist, skipping seeder...');
      return;
    }

    const permissions = [
      {
        action: PermissionsType.CREATE,
        description: 'Tạo mới',
      },
      {
        action: PermissionsType.UPDATE,
        description: 'Cập nhật',
      },
      {
        action: PermissionsType.DELETE,
        description: 'Xóa',
      },
      {
        action: PermissionsType.IMPORT,
        description: 'Nhập dữ liệu',
      },
      {
        action: PermissionsType.EXPORT,
        description: '<PERSON><PERSON>t dữ liệu',
      },
      {
        action: PermissionsType.VIEW,
        description: 'Xem',
      },
      {
        action: PermissionsType.PRINT,
        description: 'In',
      },
      {
        action: PermissionsType.ANALYTICS,
        description: 'Phân tích',
      },
    ];

    console.log('Seeding permissions...');
    
    for (const permissionData of permissions) {
      const permission = permissionRepository.create({
        action: permissionData.action,
        description: permissionData.description,
        createdBy: 'system',
      });

      await permissionRepository.save(permission);
      console.log(`Created permission: ${permission.action}`);
    }

    console.log('Permission seeding completed!');
  }
}
