import { Injectable, NotFoundException, ConflictException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { UserRoleAssignment } from '@/entities/user-role-assignment.entity';
import { User } from '@/entities/user.entity';
import { Role } from '@/entities/role.entity';
import { Department } from '@/entities/department.entity';
import { Team } from '@/entities/team.entity';
import { CreateUserRoleAssignmentDto } from './dto/create-user-role-assignment.dto';
import { UpdateUserRoleAssignmentDto } from './dto/update-user-role-assignment.dto';
import { BulkAssignRolesDto } from './dto/bulk-assign-roles.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { UserContextService, UserContext } from '@/common/services/user-context.service';
import { RoleType } from '@/common/constants';

@Injectable()
export class UserRoleAssignmentsService {
  constructor(
    @InjectRepository(UserRoleAssignment)
    private readonly userRoleAssignmentRepository: Repository<UserRoleAssignment>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Team)
    private readonly teamRepository: Repository<Team>,
    private readonly userContextService: UserContextService,
  ) {}

  async create(createDto: CreateUserRoleAssignmentDto, createdBy?: string, currentUserId?: string): Promise<UserRoleAssignment> {
    // Get current user context for permission checking
    let userContext: UserContext | null = null;
    if (currentUserId) {
      userContext = await this.userContextService.getUserContext(currentUserId);
    }

    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: createDto.userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Validate role exists
    const role = await this.roleRepository.findOne({ where: { id: createDto.roleId } });
    if (!role) {
      throw new BadRequestException('Role not found');
    }

    // Check if current user can assign this role
    if (userContext && !this.userContextService.canAssignRole(userContext.highestRole!, role)) {
      throw new ForbiddenException('You can only assign roles lower than your current role');
    }

    // Handle department assignment based on user role
    let finalDepartmentId = createDto.departmentId;

    if (userContext && userContext.highestRole?.name !== RoleType.SUPER_ADMIN) {
      // Non-super-admin users can only assign within their own department
      if (userContext.departments.length === 0) {
        throw new ForbiddenException('You must be assigned to a department to assign roles');
      }

      if (createDto.departmentId) {
        // Check if provided department is one of user's departments
        if (!this.userContextService.canManageDepartment(userContext, createDto.departmentId)) {
          throw new ForbiddenException('You can only assign roles within your own department');
        }
      } else {
        // Auto-assign to user's primary department
        finalDepartmentId = userContext.departments[0];
      }
    }

    // Validate department if provided or auto-assigned
    if (finalDepartmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: finalDepartmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Handle team assignment based on user role
    let finalTeamId = createDto.teamId;

    if (userContext && userContext.highestRole?.name !== RoleType.SUPER_ADMIN) {
      if (createDto.teamId) {
        // Check if provided team is manageable by user
        if (!this.userContextService.canManageTeam(userContext, createDto.teamId, finalDepartmentId)) {
          throw new ForbiddenException('You can only assign roles within your own team');
        }
      } else if (userContext.highestRole?.name === RoleType.TEAM_LEADER && userContext.teams.length > 0) {
        // Auto-assign to team leader's primary team
        finalTeamId = userContext.teams[0];
      }
    }

    // Validate team if provided or auto-assigned
    if (finalTeamId) {
      const team = await this.teamRepository.findOne({ where: { id: finalTeamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }

      // If team is provided, ensure it belongs to the specified department
      if (finalDepartmentId && team.departmentId !== finalDepartmentId) {
        throw new BadRequestException('Team does not belong to the specified department');
      }
    }

    // Check if assignment already exists
    const whereCondition: any = {
      userId: createDto.userId,
      roleId: createDto.roleId,
    };

    if (finalDepartmentId) {
      whereCondition.departmentId = finalDepartmentId;
    } else {
      whereCondition.departmentId = null;
    }

    if (finalTeamId) {
      whereCondition.teamId = finalTeamId;
    } else {
      whereCondition.teamId = null;
    }

    const existingAssignment = await this.userRoleAssignmentRepository.findOne({
      where: whereCondition,
    });
    if (existingAssignment) {
      throw new ConflictException('User-role assignment already exists for this context');
    }

    // Create assignment
    const assignment = this.userRoleAssignmentRepository.create({
      userId: createDto.userId,
      roleId: createDto.roleId,
      departmentId: finalDepartmentId,
      teamId: finalTeamId,
      createdBy,
    });

    return await this.userRoleAssignmentRepository.save(assignment);
  }

  async bulkAssign(bulkAssignDto: BulkAssignRolesDto, createdBy?: string, currentUserId?: string): Promise<UserRoleAssignment[]> {
    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: bulkAssignDto.userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Validate roles exist
    const roles = await this.roleRepository.find({
      where: { id: In(bulkAssignDto.roleIds) },
    });
    if (roles.length !== bulkAssignDto.roleIds.length) {
      throw new BadRequestException('One or more roles not found');
    }

    // Validate department if provided
    if (bulkAssignDto.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: bulkAssignDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team if provided
    if (bulkAssignDto.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: bulkAssignDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }

      // If team is provided, ensure it belongs to the specified department
      if (bulkAssignDto.departmentId && team.departmentId !== bulkAssignDto.departmentId) {
        throw new BadRequestException('Team does not belong to the specified department');
      }
    }

    // Check for existing assignments
    const whereCondition: any = {
      userId: bulkAssignDto.userId,
      roleId: In(bulkAssignDto.roleIds),
    };

    if (bulkAssignDto.departmentId) {
      whereCondition.departmentId = bulkAssignDto.departmentId;
    } else {
      whereCondition.departmentId = null;
    }

    if (bulkAssignDto.teamId) {
      whereCondition.teamId = bulkAssignDto.teamId;
    } else {
      whereCondition.teamId = null;
    }

    const existingAssignments = await this.userRoleAssignmentRepository.find({
      where: whereCondition,
    });

    // Filter out roles that are already assigned
    const existingRoleIds = existingAssignments.map(a => a.roleId);
    const newRoleIds = bulkAssignDto.roleIds.filter(id => !existingRoleIds.includes(id));

    if (newRoleIds.length === 0) {
      throw new ConflictException('All roles are already assigned to this user for this context');
    }

    // Create new assignments
    const assignments = newRoleIds.map(roleId =>
      this.userRoleAssignmentRepository.create({
        userId: bulkAssignDto.userId,
        roleId,
        departmentId: bulkAssignDto.departmentId,
        teamId: bulkAssignDto.teamId,
        createdBy,
      })
    );

    return await this.userRoleAssignmentRepository.save(assignments);
  }

  async findAll(paginationDto: PaginationDto, filters?: {
    userId?: string;
    roleId?: string;
    departmentId?: string;
    teamId?: string;
  }): Promise<PaginatedResult<UserRoleAssignment>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.userRoleAssignmentRepository.createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.user', 'user')
      .leftJoinAndSelect('assignment.role', 'role')
      .leftJoinAndSelect('assignment.department', 'department')
      .leftJoinAndSelect('assignment.team', 'team');
    
    // Apply filters
    if (filters?.userId) {
      queryBuilder.andWhere('assignment.userId = :userId', { userId: filters.userId });
    }
    if (filters?.roleId) {
      queryBuilder.andWhere('assignment.roleId = :roleId', { roleId: filters.roleId });
    }
    if (filters?.departmentId) {
      queryBuilder.andWhere('assignment.departmentId = :departmentId', { departmentId: filters.departmentId });
    }
    if (filters?.teamId) {
      queryBuilder.andWhere('assignment.teamId = :teamId', { teamId: filters.teamId });
    }

    // Add search functionality
    if (search) {
      queryBuilder.andWhere(
        '(user.username LIKE :search OR user.fullName LIKE :search OR user.email LIKE :search OR role.name LIKE :search OR role.displayName LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`assignment.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [assignments, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(assignments, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<UserRoleAssignment> {
    const assignment = await this.userRoleAssignmentRepository.findOne({
      where: { id },
      relations: ['user', 'role', 'department', 'team'],
    });

    if (!assignment) {
      throw new NotFoundException(`User-role assignment with ID ${id} not found`);
    }

    return assignment;
  }

  async update(id: string, updateDto: UpdateUserRoleAssignmentDto, updatedBy?: string): Promise<UserRoleAssignment> {
    const assignment = await this.findOne(id);

    // Validate department if being updated
    if (updateDto.departmentId && updateDto.departmentId !== assignment.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: updateDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team if being updated
    if (updateDto.teamId && updateDto.teamId !== assignment.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: updateDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }

      // If team is being updated, ensure it belongs to the department
      const targetDepartmentId = updateDto.departmentId || assignment.departmentId;
      if (targetDepartmentId && team.departmentId !== targetDepartmentId) {
        throw new BadRequestException('Team does not belong to the specified department');
      }
    }

    // Update assignment
    Object.assign(assignment, updateDto, { updatedBy });
    return await this.userRoleAssignmentRepository.save(assignment);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    const assignment = await this.findOne(id);
    
    // Soft delete
    assignment.deletedBy = deletedBy;
    await this.userRoleAssignmentRepository.softRemove(assignment);
  }

  async removeByUserAndRoles(userId: string, roleIds: string[], departmentId?: string, teamId?: string): Promise<void> {
    const whereCondition: any = {
      userId,
      roleId: In(roleIds),
    };

    if (departmentId) {
      whereCondition.departmentId = departmentId;
    } else {
      whereCondition.departmentId = null;
    }

    if (teamId) {
      whereCondition.teamId = teamId;
    } else {
      whereCondition.teamId = null;
    }

    await this.userRoleAssignmentRepository.delete(whereCondition);
  }

  async getUserRoles(userId: string, departmentId?: string, teamId?: string): Promise<Role[]> {
    const queryBuilder = this.userRoleAssignmentRepository.createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.role', 'role')
      .where('assignment.userId = :userId', { userId });

    if (departmentId) {
      queryBuilder.andWhere('assignment.departmentId = :departmentId', { departmentId });
    }
    if (teamId) {
      queryBuilder.andWhere('assignment.teamId = :teamId', { teamId });
    }

    const assignments = await queryBuilder.getMany();
    return assignments.map(assignment => assignment.role);
  }

  async getUsersByRole(roleId: string, departmentId?: string, teamId?: string): Promise<User[]> {
    const queryBuilder = this.userRoleAssignmentRepository.createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.user', 'user')
      .where('assignment.roleId = :roleId', { roleId });

    if (departmentId) {
      queryBuilder.andWhere('assignment.departmentId = :departmentId', { departmentId });
    }
    if (teamId) {
      queryBuilder.andWhere('assignment.teamId = :teamId', { teamId });
    }

    const assignments = await queryBuilder.getMany();
    return assignments.map(assignment => assignment.user);
  }
}
