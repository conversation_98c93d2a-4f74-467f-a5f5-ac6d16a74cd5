import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, Like, FindManyOptions } from 'typeorm';
import { Cost, CostStatus, CostType } from '../../entities/cost.entity';
import { Department } from '../../entities/department.entity';
import { User } from '../../entities/user.entity';
import { CreateCostDto } from './dto/create-cost.dto';
import { UpdateCostDto } from './dto/update-cost.dto';
import { CostQueryDto } from './dto/cost-query.dto';
import { CostResponseDto } from './dto/cost-response.dto';
import { 
  CostReportQueryDto, 
  CostReportResponseDto, 
  CostReportSummaryDto, 
  CostDepartmentStatsDto, 
  CostTimeSeriesDto, 
  CostTopSpendersDto, 
  CostReportPeriod,
  ApproveCostDto 
} from './dto/cost-report.dto';
import { PaginatedResult, createPaginatedResult } from '../../common/dto/pagination.dto';

@Injectable()
export class CostsService {
  constructor(
    @InjectRepository(Cost)
    private costRepository: Repository<Cost>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createCostDto: CreateCostDto, user: User): Promise<CostResponseDto> {
    // Determine department - use provided department_id or user's department
    let departmentId = createCostDto.department_id;
    if (!departmentId) {
      departmentId = user.userRoles?.find(ur => ur.department)?.department?.id;
    }

    if (!departmentId) {
      throw new BadRequestException('Department is required');
    }

    const department = await this.departmentRepository.findOne({
      where: { id: departmentId }
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    const cost = this.costRepository.create({
      ...createCostDto,
      department_id: departmentId,
      created_by: user.id,
      costDate: new Date(createCostDto.costDate),
      paymentDate: createCostDto.paymentDate ? new Date(createCostDto.paymentDate) : undefined,
    });

    const savedCost = await this.costRepository.save(cost);
    
    // Load relations for response
    const costWithRelations = await this.costRepository.findOne({
      where: { id: savedCost.id },
      relations: ['department', 'createdByUser', 'approvedByUser']
    });

    return this.transformToResponseDto(costWithRelations!);
  }

  async findAll(queryDto: CostQueryDto): Promise<PaginatedResult<CostResponseDto>> {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      type, 
      status, 
      departmentId, 
      startDate, 
      endDate, 
      minAmount, 
      maxAmount, 
      vendor, 
      isApproved, 
      isOverdue, 
      hasDocuments, 
      createdBy, 
      sortBy = 'createdAt', 
      sortOrder = 'DESC' 
    } = queryDto;

    const queryBuilder = this.costRepository
      .createQueryBuilder('cost')
      .leftJoinAndSelect('cost.department', 'department')
      .leftJoinAndSelect('cost.createdByUser', 'createdByUser')
      .leftJoinAndSelect('cost.approvedByUser', 'approvedByUser');

    // Apply filters
    if (search) {
      queryBuilder.andWhere(
        '(cost.name ILIKE :search OR cost.description ILIKE :search OR cost.vendor ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (type) {
      queryBuilder.andWhere('cost.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('cost.status = :status', { status });
    }

    if (departmentId) {
      queryBuilder.andWhere('cost.department_id = :departmentId', { departmentId });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('cost.costDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate
      });
    }

    if (minAmount !== undefined) {
      queryBuilder.andWhere('cost.amount >= :minAmount', { minAmount });
    }

    if (maxAmount !== undefined) {
      queryBuilder.andWhere('cost.amount <= :maxAmount', { maxAmount });
    }

    if (vendor) {
      queryBuilder.andWhere('cost.vendor ILIKE :vendor', { vendor: `%${vendor}%` });
    }

    if (isApproved !== undefined) {
      queryBuilder.andWhere('cost.isApproved = :isApproved', { isApproved });
    }

    if (createdBy) {
      queryBuilder.andWhere('cost.created_by = :createdBy', { createdBy });
    }

    // Handle complex filters
    if (hasDocuments !== undefined) {
      if (hasDocuments) {
        queryBuilder.andWhere(
          '(cost.googleDriveLink IS NOT NULL OR cost.additionalLinks IS NOT NULL)'
        );
      } else {
        queryBuilder.andWhere(
          '(cost.googleDriveLink IS NULL AND cost.additionalLinks IS NULL)'
        );
      }
    }

    // Apply sorting
    queryBuilder.orderBy(`cost.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [costs, total] = await queryBuilder.getManyAndCount();

    const transformedCosts = costs.map(cost => this.transformToResponseDto(cost));

    return createPaginatedResult(transformedCosts, total, page, limit);
  }

  async findOne(id: string): Promise<CostResponseDto> {
    const cost = await this.costRepository.findOne({
      where: { id },
      relations: ['department', 'createdByUser', 'approvedByUser']
    });

    if (!cost) {
      throw new NotFoundException('Cost not found');
    }

    return this.transformToResponseDto(cost);
  }

  async update(id: string, updateCostDto: UpdateCostDto, user: User): Promise<CostResponseDto> {
    const cost = await this.costRepository.findOne({
      where: { id },
      relations: ['department', 'createdByUser']
    });

    if (!cost) {
      throw new NotFoundException('Cost not found');
    }

    // Check if user can update this cost
    if (cost.created_by !== user.id && !this.canUserManageCosts(user)) {
      throw new ForbiddenException('You can only update your own costs');
    }

    // Handle department change
    if (updateCostDto.department_id && updateCostDto.department_id !== cost.department_id) {
      const department = await this.departmentRepository.findOne({
        where: { id: updateCostDto.department_id }
      });

      if (!department) {
        throw new NotFoundException('Department not found');
      }

      cost.department = department;
      cost.department_id = updateCostDto.department_id;
    }

    // Update fields
    Object.assign(cost, {
      ...updateCostDto,
      costDate: updateCostDto.costDate ? new Date(updateCostDto.costDate) : cost.costDate,
      paymentDate: updateCostDto.paymentDate ? new Date(updateCostDto.paymentDate) : cost.paymentDate,
    });

    const updatedCost = await this.costRepository.save(cost);
    
    // Load relations for response
    const costWithRelations = await this.costRepository.findOne({
      where: { id: updatedCost.id },
      relations: ['department', 'createdByUser', 'approvedByUser']
    });

    return this.transformToResponseDto(costWithRelations!);
  }

  async remove(id: string, user: User): Promise<void> {
    const cost = await this.costRepository.findOne({
      where: { id },
      relations: ['createdByUser']
    });

    if (!cost) {
      throw new NotFoundException('Cost not found');
    }

    // Check if user can delete this cost
    if (cost.created_by !== user.id && !this.canUserManageCosts(user)) {
      throw new ForbiddenException('You can only delete your own costs');
    }

    await this.costRepository.remove(cost);
  }

  async approveCost(id: string, approveDto: ApproveCostDto, user: User): Promise<CostResponseDto> {
    if (!this.canUserApproveCosts(user)) {
      throw new ForbiddenException('You do not have permission to approve costs');
    }

    const cost = await this.costRepository.findOne({
      where: { id },
      relations: ['department', 'createdByUser']
    });

    if (!cost) {
      throw new NotFoundException('Cost not found');
    }

    cost.isApproved = approveDto.isApproved;
    cost.approvedAt = new Date();
    cost.approvedBy = user.id;
    cost.approvedByUser = user;

    if (approveDto.isApproved) {
      cost.status = CostStatus.APPROVED;
      cost.rejectionReason = undefined;
    } else {
      cost.status = CostStatus.REJECTED;
      cost.rejectionReason = approveDto.rejectionReason;
    }

    if (approveDto.notes) {
      cost.notes = cost.notes ? `${cost.notes}\n---\n${approveDto.notes}` : approveDto.notes;
    }

    const updatedCost = await this.costRepository.save(cost);
    
    // Load relations for response
    const costWithRelations = await this.costRepository.findOne({
      where: { id: updatedCost.id },
      relations: ['department', 'createdByUser', 'approvedByUser']
    });

    return this.transformToResponseDto(costWithRelations!);
  }

  async getCostReport(queryDto: CostReportQueryDto): Promise<CostReportResponseDto> {
    const { 
      startDate, 
      endDate, 
      departmentId, 
      departmentName, 
      type, 
      status, 
      period = CostReportPeriod.MONTHLY,
      page = 1,
      limit = 10
    } = queryDto;

    // Calculate date range
    const dateRange = this.calculateDateRange(startDate, endDate);

    // Build base query
    const baseQuery = this.costRepository
      .createQueryBuilder('cost')
      .leftJoinAndSelect('cost.department', 'department')
      .leftJoinAndSelect('cost.createdByUser', 'createdByUser')
      .where('cost.costDate BETWEEN :startDate AND :endDate', {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });

    // Apply filters
    if (departmentId) {
      baseQuery.andWhere('cost.department_id = :departmentId', { departmentId });
    }

    if (departmentName) {
      baseQuery.andWhere('department.name ILIKE :departmentName', { 
        departmentName: `%${departmentName}%` 
      });
    }

    if (type) {
      baseQuery.andWhere('cost.type = :type', { type });
    }

    if (status) {
      baseQuery.andWhere('cost.status = :status', { status });
    }

    // Get all costs in the date range
    const costs = await baseQuery.getMany();

    // Generate report data
    const [summary, departmentStats, timeSeries, topSpenders] = await Promise.all([
      this.generateCostSummary(costs, dateRange),
      this.generateDepartmentStats(costs, page, limit),
      this.generateTimeSeries(costs, period, dateRange),
      this.generateTopSpenders(costs)
    ]);

    // Calculate pagination
    const totalDepartments = await this.departmentRepository.count();
    const totalPages = Math.ceil(totalDepartments / limit);

    return {
      summary,
      departmentStats,
      timeSeries,
      topSpenders,
      dateRange: {
        startDate: dateRange.startDate.toISOString().split('T')[0],
        endDate: dateRange.endDate.toISOString().split('T')[0]
      },
      period,
      pagination: {
        page,
        limit,
        total: totalDepartments,
        totalPages
      },
      generatedAt: new Date()
    };
  }

  private calculateDateRange(startDate?: string, endDate?: string): { startDate: Date; endDate: Date } {
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 999);
    
    return { startDate: start, endDate: end };
  }

  private async generateCostSummary(costs: Cost[], dateRange: { startDate: Date; endDate: Date }): Promise<CostReportSummaryDto> {
    const totalCostCount = costs.length;
    const totalAmount = costs.reduce((sum, cost) => sum + Number(cost.amount), 0);
    const averageAmount = totalCostCount > 0 ? totalAmount / totalCostCount : 0;

    const approvedCount = costs.filter(c => c.isApproved).length;
    const pendingCount = costs.filter(c => c.status === CostStatus.PENDING).length;
    const paidCount = costs.filter(c => c.status === CostStatus.PAID).length;
    const overdueCount = costs.filter(c => c.isOverdue).length;

    const approvalRate = totalCostCount > 0 ? (approvedCount / totalCostCount) * 100 : 0;
    const paymentRate = totalCostCount > 0 ? (paidCount / totalCostCount) * 100 : 0;

    // Calculate growth rate
    const periodDuration = dateRange.endDate.getTime() - dateRange.startDate.getTime();
    const previousPeriodStart = new Date(dateRange.startDate.getTime() - periodDuration);
    const previousPeriodEnd = new Date(dateRange.endDate.getTime() - periodDuration);

    const previousCosts = await this.costRepository
      .createQueryBuilder('cost')
      .where('cost.costDate BETWEEN :startDate AND :endDate', {
        startDate: previousPeriodStart,
        endDate: previousPeriodEnd
      })
      .getMany();

    const previousTotalAmount = previousCosts.reduce((sum, cost) => sum + Number(cost.amount), 0);
    const growthRate = previousTotalAmount > 0 ? ((totalAmount - previousTotalAmount) / previousTotalAmount) * 100 : 0;

    const totalDepartments = await this.departmentRepository.count();

    // Generate type breakdown
    const typeBreakdown = {} as any;
    Object.values(CostType).forEach(type => {
      const typeCosts = costs.filter(c => c.type === type);
      const typeAmount = typeCosts.reduce((sum, cost) => sum + Number(cost.amount), 0);
      typeBreakdown[type] = {
        count: typeCosts.length,
        amount: typeAmount,
        percentage: totalAmount > 0 ? (typeAmount / totalAmount) * 100 : 0
      };
    });

    return {
      totalCostCount,
      totalAmount,
      averageAmount,
      approvedCount,
      pendingCount,
      paidCount,
      overdueCount,
      approvalRate,
      paymentRate,
      growthRate,
      totalDepartments,
      typeBreakdown
    };
  }

  private async generateDepartmentStats(costs: Cost[], page: number, limit: number): Promise<CostDepartmentStatsDto[]> {
    const skip = (page - 1) * limit;
    
    const departments = await this.departmentRepository.find({
      skip,
      take: limit,
      order: { name: 'ASC' }
    });

    const departmentStats: CostDepartmentStatsDto[] = [];

    for (const department of departments) {
      const departmentCosts = costs.filter(c => c.department_id === department.id);
      
      const totalCostCount = departmentCosts.length;
      const totalAmount = departmentCosts.reduce((sum, cost) => sum + Number(cost.amount), 0);
      const averageAmount = totalCostCount > 0 ? totalAmount / totalCostCount : 0;
      
      const amounts = departmentCosts.map(c => Number(c.amount));
      const maxAmount = amounts.length > 0 ? Math.max(...amounts) : 0;
      const minAmount = amounts.length > 0 ? Math.min(...amounts) : 0;

      const approvedCount = departmentCosts.filter(c => c.isApproved).length;
      const pendingCount = departmentCosts.filter(c => c.status === CostStatus.PENDING).length;
      const paidCount = departmentCosts.filter(c => c.status === CostStatus.PAID).length;
      const overdueCount = departmentCosts.filter(c => c.isOverdue).length;

      const approvalRate = totalCostCount > 0 ? (approvedCount / totalCostCount) * 100 : 0;
      const paymentRate = totalCostCount > 0 ? (paidCount / totalCostCount) * 100 : 0;

      // Generate cost type breakdown
      const costTypeBreakdown = {} as any;
      Object.values(CostType).forEach(type => {
        const typeCosts = departmentCosts.filter(c => c.type === type);
        const typeAmount = typeCosts.reduce((sum, cost) => sum + Number(cost.amount), 0);
        costTypeBreakdown[type] = {
          count: typeCosts.length,
          amount: typeAmount,
          percentage: totalAmount > 0 ? (typeAmount / totalAmount) * 100 : 0
        };
      });

      departmentStats.push({
        departmentId: department.id,
        departmentName: department.name,
        totalCostCount,
        totalAmount,
        averageAmount,
        maxAmount,
        minAmount,
        approvedCount,
        pendingCount,
        paidCount,
        overdueCount,
        approvalRate,
        paymentRate,
        costTypeBreakdown,
        createdAt: department.createdAt,
        updatedAt: department.updatedAt
      });
    }

    return departmentStats.sort((a, b) => b.totalAmount - a.totalAmount);
  }

  private generateTimeSeries(costs: Cost[], period: CostReportPeriod, dateRange: { startDate: Date; endDate: Date }): CostTimeSeriesDto[] {
    const timeSeries: CostTimeSeriesDto[] = [];
    
    const intervals = this.generateDateIntervals(dateRange.startDate, dateRange.endDate, period);
    
    for (const interval of intervals) {
      const intervalCosts = costs.filter(c => 
        c.costDate >= interval.start && c.costDate <= interval.end
      );
      
      const costCount = intervalCosts.length;
      const totalAmount = intervalCosts.reduce((sum, cost) => sum + Number(cost.amount), 0);
      const averageAmount = costCount > 0 ? totalAmount / costCount : 0;
      
      const approvedCount = intervalCosts.filter(c => c.isApproved).length;
      const paidCount = intervalCosts.filter(c => c.status === CostStatus.PAID).length;

      timeSeries.push({
        date: interval.start.toISOString().split('T')[0],
        costCount,
        totalAmount,
        averageAmount,
        approvedCount,
        paidCount
      });
    }
    
    return timeSeries;
  }

  private generateTopSpenders(costs: Cost[]): CostTopSpendersDto[] {
    const departmentMap = new Map<string, { name: string; costs: Cost[] }>();
    
    for (const cost of costs) {
      if (!departmentMap.has(cost.department_id)) {
        departmentMap.set(cost.department_id, {
          name: cost.department.name,
          costs: []
        });
      }
      departmentMap.get(cost.department_id)!.costs.push(cost);
    }

    const topSpenders: CostTopSpendersDto[] = [];
    const totalAmount = costs.reduce((sum, cost) => sum + Number(cost.amount), 0);

    for (const [departmentId, departmentData] of departmentMap.entries()) {
      const departmentTotal = departmentData.costs.reduce((sum, cost) => sum + Number(cost.amount), 0);
      const costCount = departmentData.costs.length;
      const averageAmount = costCount > 0 ? departmentTotal / costCount : 0;
      const percentage = totalAmount > 0 ? (departmentTotal / totalAmount) * 100 : 0;

      topSpenders.push({
        departmentId: departmentId,
        departmentName: departmentData.name,
        totalAmount: departmentTotal,
        costCount,
        averageAmount,
        rank: 0, // Will be set after sorting
        percentage
      });
    }

    // Sort by total amount and assign ranks
    topSpenders.sort((a, b) => b.totalAmount - a.totalAmount);
    topSpenders.forEach((spender, index) => {
      spender.rank = index + 1;
    });

    return topSpenders.slice(0, 10); // Return top 10
  }

  private generateDateIntervals(startDate: Date, endDate: Date, period: CostReportPeriod): { start: Date; end: Date }[] {
    const intervals: { start: Date; end: Date }[] = [];
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const intervalStart = new Date(currentDate);
      let intervalEnd: Date;

      switch (period) {
        case CostReportPeriod.DAILY:
          intervalEnd = new Date(currentDate);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        case CostReportPeriod.WEEKLY:
          intervalEnd = new Date(currentDate);
          intervalEnd.setDate(intervalEnd.getDate() + 6);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        case CostReportPeriod.MONTHLY:
          intervalEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        case CostReportPeriod.YEARLY:
          intervalEnd = new Date(currentDate.getFullYear(), 11, 31);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setFullYear(currentDate.getFullYear() + 1);
          break;
      }

      intervals.push({ start: intervalStart, end: intervalEnd });
    }

    return intervals;
  }

  private transformToResponseDto(cost: Cost): CostResponseDto {
    return {
      id: cost.id,
      name: cost.name,
      description: cost.description,
      amount: Number(cost.amount),
      formattedAmount: cost.formattedAmount,
      currency: cost.currency,
      type: cost.type,
      status: cost.status,
      costDate: cost.costDate,
      paymentDate: cost.paymentDate,
      vendor: cost.vendor,
      invoiceNumber: cost.invoiceNumber,
      googleDriveLink: cost.googleDriveLink,
      additionalLinks: cost.additionalLinks,
      fileName: cost.fileName,
      fileType: cost.fileType,
      notes: cost.notes,
      isApproved: cost.isApproved,
      approvedAt: cost.approvedAt,
      approvedBy: cost.approvedBy,
      rejectionReason: cost.rejectionReason,
      isOverdue: cost.isOverdue,
      hasDocuments: cost.hasDocuments,
      department: {
        id: cost.department.id,
        name: cost.department.name
      },
      createdByUser: {
        id: cost.createdByUser.id,
        username: cost.createdByUser.username,
        fullName: cost.createdByUser.fullName
      },
      approvedByUser: cost.approvedByUser ? {
        id: cost.approvedByUser.id,
        username: cost.approvedByUser.username,
        fullName: cost.approvedByUser.fullName
      } : undefined,
      createdAt: cost.createdAt,
      updatedAt: cost.updatedAt
    };
  }

  private canUserManageCosts(user: User): boolean {
    // Check if user has manager role or higher
    return user.userRoles?.some(ur => 
      ['SUPER_ADMIN', 'MANAGER'].includes(ur.role.name)
    ) || false;
  }

  private canUserApproveCosts(user: User): boolean {
    // Check if user has team leader role or higher
    return user.userRoles?.some(ur => 
      ['SUPER_ADMIN', 'MANAGER', 'TEAM_LEADER'].includes(ur.role.name)
    ) || false;
  }
} 