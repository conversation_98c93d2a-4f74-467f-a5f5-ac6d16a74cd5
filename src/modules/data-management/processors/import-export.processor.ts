import { Processor, Process } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bull';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parser';
import { createObjectCsvWriter } from 'csv-writer';
import { User, Affiliate, Bet, Deposit, AuditLog } from '@/entities';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { QUEUE_NAMES, JOB_TYPES } from '@/common/queue/queue.constants';

export interface ImportJobData {
  filePath: string;
  entityType: string;
  userId: number;
  options?: {
    skipFirstRow?: boolean;
    delimiter?: string;
    encoding?: string;
  };
}

export interface ExportJobData {
  entityType: string;
  userId: number;
  filters?: any;
  outputPath?: string;
  format?: 'csv' | 'json' | 'xlsx';
  options?: {
    includeHeaders?: boolean;
    delimiter?: string;
    encoding?: string;
  };
}

@Injectable()
@Processor(QUEUE_NAMES.IMPORT_EXPORT)
export class ImportExportProcessor {
  constructor(
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(Affiliate) private affiliateRepository: Repository<Affiliate>,
    @InjectRepository(Bet) private betRepository: Repository<Bet>,
    @InjectRepository(Deposit) private depositRepository: Repository<Deposit>,
    @InjectRepository(AuditLog) private auditLogRepository: Repository<AuditLog>,
    private readonly logger: CustomLoggerService,
  ) {}

  @Process(JOB_TYPES.IMPORT_USERS)
  async importUsers(job: Job<ImportJobData>) {
    const { filePath, userId, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting user import from ${filePath}`, 'ImportExportProcessor');

      const users = await this.parseCSVFile(filePath, options);
      const totalUsers = users.length;
      let processedUsers = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const userData of users) {
        try {
          // Validate required fields
          if (!userData.username || !userData.email) {
            throw new Error('Username and email are required');
          }

          // Check if user already exists
          const existingUser = await this.userRepository.findOne({
            where: [
              { username: userData.username },
              { email: userData.email }
            ]
          });

          if (existingUser) {
            throw new Error(`User with username ${userData.username} or email ${userData.email} already exists`);
          }

          // Create new user
          const user = this.userRepository.create({
            username: userData.username,
            email: userData.email,
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            phone: userData.phone || null,
            isActive: userData.isActive !== undefined ? userData.isActive : true,
            // Add other fields as needed
          });

          await this.userRepository.save(user);
          successCount++;

          // Log audit trail
          await this.auditLogRepository.save({
            userId,
            action: 'IMPORT_USER',
            entityType: 'User',
            entityId: user.id,
            details: `Imported user: ${user.username}`,
            ipAddress: '127.0.0.1', // System import
          });

        } catch (error) {
          errorCount++;
          errors.push(`Row ${processedUsers + 1}: ${error.message}`);
          this.logger.warn(`Failed to import user at row ${processedUsers + 1}: ${error.message}`, 'ImportExportProcessor');
        }

        processedUsers++;
        await job.progress(Math.round((processedUsers / totalUsers) * 100));
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const result = {
        totalProcessed: processedUsers,
        successCount,
        errorCount,
        errors: errors.slice(0, 100), // Limit errors to prevent memory issues
      };

      this.logger.info(
        `User import completed: ${successCount} success, ${errorCount} errors`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`User import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.EXPORT_USERS)
  async exportUsers(job: Job<ExportJobData>) {
    const { userId, filters = {}, outputPath, format = 'csv', options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info('Starting user export', 'ImportExportProcessor');

      // Build query
      const queryBuilder = this.userRepository.createQueryBuilder('user')
        .leftJoinAndSelect('user.department', 'department')
        .leftJoinAndSelect('user.team', 'team');

      // Apply filters
      if (filters.isActive !== undefined) {
        queryBuilder.andWhere('user.isActive = :isActive', { isActive: filters.isActive });
      }
      if (filters.departmentId) {
        queryBuilder.andWhere('user.departmentId = :departmentId', { departmentId: filters.departmentId });
      }
      if (filters.teamId) {
        queryBuilder.andWhere('user.teamId = :teamId', { teamId: filters.teamId });
      }
      if (filters.createdFrom) {
        queryBuilder.andWhere('user.createdAt >= :createdFrom', { createdFrom: filters.createdFrom });
      }
      if (filters.createdTo) {
        queryBuilder.andWhere('user.createdAt <= :createdTo', { createdTo: filters.createdTo });
      }

      const users = await queryBuilder.getMany();
      await job.progress(50);

      // Generate output file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `users_export_${timestamp}.${format}`;
      const filePath = outputPath || path.join('./uploads/exports', fileName);

      // Ensure export directory exists
      const exportDir = path.dirname(filePath);
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      if (format === 'csv') {
        await this.exportToCSV(users, filePath, {
          includeHeaders: options.includeHeaders !== false,
          delimiter: options.delimiter || ',',
        });
      } else if (format === 'json') {
        await this.exportToJSON(users, filePath);
      }

      await job.progress(100);

      // Log audit trail
      await this.auditLogRepository.save({
        userId,
        action: 'EXPORT_USERS',
        entityType: 'User',
        details: `Exported ${users.length} users to ${fileName}`,
        ipAddress: '127.0.0.1',
      });

      const result = {
        fileName,
        filePath,
        recordCount: users.length,
        format,
      };

      this.logger.info(
        `User export completed: ${users.length} records exported to ${fileName}`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`User export failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.IMPORT_AFFILIATES)
  async importAffiliates(job: Job<ImportJobData>) {
    const { filePath, userId, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting affiliate import from ${filePath}`, 'ImportExportProcessor');

      const affiliates = await this.parseCSVFile(filePath, options);
      const totalAffiliates = affiliates.length;
      let processedAffiliates = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const affiliateData of affiliates) {
        try {
          // Validate required fields
          if (!affiliateData.code || !affiliateData.name) {
            throw new Error('Code and name are required');
          }

          // Check if affiliate already exists
          const existingAffiliate = await this.affiliateRepository.findOne({
            where: { code: affiliateData.code }
          });

          if (existingAffiliate) {
            throw new Error(`Affiliate with code ${affiliateData.code} already exists`);
          }

          // Create new affiliate
          const affiliate = this.affiliateRepository.create({
            code: affiliateData.code,
            name: affiliateData.name,
            description: affiliateData.description || '',
            commissionRate: parseFloat(affiliateData.commissionRate) || 0,
            isActive: affiliateData.isActive !== undefined ? affiliateData.isActive : true,
            // Add other fields as needed
          });

          await this.affiliateRepository.save(affiliate);
          successCount++;

          // Log audit trail
          await this.auditLogRepository.save({
            userId,
            action: 'IMPORT_AFFILIATE',
            entityType: 'Affiliate',
            entityId: affiliate.id,
            details: `Imported affiliate: ${affiliate.code}`,
            ipAddress: '127.0.0.1',
          });

        } catch (error) {
          errorCount++;
          errors.push(`Row ${processedAffiliates + 1}: ${error.message}`);
          this.logger.warn(`Failed to import affiliate at row ${processedAffiliates + 1}: ${error.message}`, 'ImportExportProcessor');
        }

        processedAffiliates++;
        await job.progress(Math.round((processedAffiliates / totalAffiliates) * 100));
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const result = {
        totalProcessed: processedAffiliates,
        successCount,
        errorCount,
        errors: errors.slice(0, 100),
      };

      this.logger.info(
        `Affiliate import completed: ${successCount} success, ${errorCount} errors`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Affiliate import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.EXPORT_AFFILIATES)
  async exportAffiliates(job: Job<ExportJobData>) {
    const { userId, filters = {}, outputPath, format = 'csv', options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info('Starting affiliate export', 'ImportExportProcessor');

      // Build query
      const queryBuilder = this.affiliateRepository.createQueryBuilder('affiliate');

      // Apply filters
      if (filters.isActive !== undefined) {
        queryBuilder.andWhere('affiliate.isActive = :isActive', { isActive: filters.isActive });
      }
      if (filters.createdFrom) {
        queryBuilder.andWhere('affiliate.createdAt >= :createdFrom', { createdFrom: filters.createdFrom });
      }
      if (filters.createdTo) {
        queryBuilder.andWhere('affiliate.createdAt <= :createdTo', { createdTo: filters.createdTo });
      }

      const affiliates = await queryBuilder.getMany();
      await job.progress(50);

      // Generate output file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `affiliates_export_${timestamp}.${format}`;
      const filePath = outputPath || path.join('./uploads/exports', fileName);

      // Ensure export directory exists
      const exportDir = path.dirname(filePath);
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      if (format === 'csv') {
        await this.exportToCSV(affiliates, filePath, {
          includeHeaders: options.includeHeaders !== false,
          delimiter: options.delimiter || ',',
        });
      } else if (format === 'json') {
        await this.exportToJSON(affiliates, filePath);
      }

      await job.progress(100);

      // Log audit trail
      await this.auditLogRepository.save({
        userId,
        action: 'EXPORT_AFFILIATES',
        entityType: 'Affiliate',
        details: `Exported ${affiliates.length} affiliates to ${fileName}`,
        ipAddress: '127.0.0.1',
      });

      const result = {
        fileName,
        filePath,
        recordCount: affiliates.length,
        format,
      };

      this.logger.info(
        `Affiliate export completed: ${affiliates.length} records exported to ${fileName}`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Affiliate export failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  /**
   * Parse CSV file and return array of objects
   */
  private async parseCSVFile(filePath: string, options: any = {}): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      const { skipFirstRow = true, delimiter = ',', encoding = 'utf8' } = options;

      if (!fs.existsSync(filePath)) {
        reject(new Error(`File not found: ${filePath}`));
        return;
      }

      fs.createReadStream(filePath, { encoding })
        .pipe(csv({
          separator: delimiter,
          skipEmptyLines: true,
          skipLinesWithError: true,
        }))
        .on('data', (data) => {
          results.push(data);
        })
        .on('end', () => {
          resolve(results);
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }

  /**
   * Export data to CSV file
   */
  private async exportToCSV(data: any[], filePath: string, options: any = {}): Promise<void> {
    const { includeHeaders = true, delimiter = ',' } = options;

    if (data.length === 0) {
      // Create empty file
      fs.writeFileSync(filePath, '');
      return;
    }

    // Get headers from first object
    const headers = Object.keys(data[0]).map(key => ({
      id: key,
      title: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
    }));

    const csvWriter = createObjectCsvWriter({
      path: filePath,
      header: headers,
      fieldDelimiter: delimiter,
      encoding: 'utf8',
    });

    await csvWriter.writeRecords(data);
  }

  /**
   * Export data to JSON file
   */
  private async exportToJSON(data: any[], filePath: string): Promise<void> {
    const jsonData = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonData, 'utf8');
  }
}
