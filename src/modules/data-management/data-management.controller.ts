import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseUUI<PERSON>ipe,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { DataManagementService } from './data-management.service';
import { CreateUserAnalyticsDto } from './dto/create-user-analytics.dto';
import { UpdateUserAnalyticsDto } from './dto/update-user-analytics.dto';
import { UserAnalyticsQueryDto } from './dto/user-analytics-query.dto';
import { UserAnalyticsResponseDto } from './dto/user-analytics-response.dto';
import { DataManagementReportQueryDto, DataManagementReportDto } from './dto/data-management-report.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { Permissions } from '../../common/decorators/permissions.decorator';
import { RoleType } from '../../common/constants/role.constant';
import { PermissionsType } from '../../common/constants/permissions.constant';

@ApiTags('Data Management')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@Controller('data-management')
export class DataManagementController {
  constructor(private readonly dataManagementService: DataManagementService) {}

  // User Analytics CRUD Operations
  @Post('user-analytics')
  @HttpCode(HttpStatus.CREATED)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.CREATE)
  @ApiOperation({ 
    summary: 'Tạo mới dữ liệu analytics người dùng',
    description: 'Tạo mới bản ghi analytics cho người dùng với thông tin đăng ký, hoạt động và metrics'
  })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Dữ liệu analytics người dùng đã được tạo thành công',
    type: UserAnalyticsResponseDto
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Dữ liệu đầu vào không hợp lệ hoặc analytics đã tồn tại cho người dùng này' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy người dùng hoặc phòng ban' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async createUserAnalytics(@Body() createDto: CreateUserAnalyticsDto): Promise<UserAnalyticsResponseDto> {
    return this.dataManagementService.createUserAnalytics(createDto);
  }

  @Get('user-analytics')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy danh sách analytics người dùng',
    description: 'Lấy danh sách analytics người dùng với phân trang, lọc và tìm kiếm nâng cao'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Danh sách analytics người dùng',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/UserAnalyticsResponseDto' }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async findAllUserAnalytics(@Query() query: UserAnalyticsQueryDto): Promise<{
    data: UserAnalyticsResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    return this.dataManagementService.findAllUserAnalytics(query);
  }

  @Get('user-analytics/:id')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy chi tiết analytics người dùng',
    description: 'Lấy thông tin chi tiết của bản ghi analytics người dùng'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chi tiết analytics người dùng',
    type: UserAnalyticsResponseDto
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy dữ liệu analytics người dùng' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async findOneUserAnalytics(@Param('id', ParseUUIDPipe) id: string): Promise<UserAnalyticsResponseDto> {
    return this.dataManagementService.findOneUserAnalytics(id);
  }

  @Patch('user-analytics/:id')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ 
    summary: 'Cập nhật analytics người dùng',
    description: 'Cập nhật thông tin analytics người dùng và tính toán lại các metrics'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Dữ liệu analytics người dùng đã được cập nhật thành công',
    type: UserAnalyticsResponseDto
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Dữ liệu đầu vào không hợp lệ' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy dữ liệu analytics người dùng hoặc phòng ban' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async updateUserAnalytics(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateUserAnalyticsDto
  ): Promise<UserAnalyticsResponseDto> {
    return this.dataManagementService.updateUserAnalytics(id, updateDto);
  }

  @Delete('user-analytics/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({ 
    summary: 'Xóa analytics người dùng',
    description: 'Xóa bản ghi analytics người dùng khỏi hệ thống'
  })
  @ApiResponse({ 
    status: HttpStatus.NO_CONTENT, 
    description: 'Dữ liệu analytics người dùng đã được xóa thành công' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy dữ liệu analytics người dùng' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async removeUserAnalytics(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.dataManagementService.removeUserAnalytics(id);
  }

  // Comprehensive Report Generation
  @Get('reports/comprehensive')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Tạo báo cáo tổng hợp quản lý dữ liệu',
    description: 'Tạo báo cáo tổng hợp bao gồm đăng ký, analytics thành viên, phân tích FTD và lifecycle'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Báo cáo tổng hợp quản lý dữ liệu',
    type: DataManagementReportDto
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async generateComprehensiveReport(@Query() query: DataManagementReportQueryDto): Promise<DataManagementReportDto> {
    return this.dataManagementService.generateReport({ ...query, report_type: 'comprehensive' });
  }

  @Get('reports/registration')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Tạo báo cáo đăng ký',
    description: 'Tạo báo cáo chi tiết về đăng ký người dùng với phân tích nguồn và time series'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Báo cáo đăng ký người dùng',
    type: DataManagementReportDto
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async generateRegistrationReport(@Query() query: DataManagementReportQueryDto): Promise<DataManagementReportDto> {
    return this.dataManagementService.generateReport({ ...query, report_type: 'registration' });
  }

  @Get('reports/member-analytics')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Tạo báo cáo analytics thành viên',
    description: 'Tạo báo cáo về hoạt động thành viên, lifecycle và phân bố trạng thái'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Báo cáo analytics thành viên',
    type: DataManagementReportDto
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async generateMemberAnalyticsReport(@Query() query: DataManagementReportQueryDto): Promise<DataManagementReportDto> {
    return this.dataManagementService.generateReport({ ...query, report_type: 'member_analytics' });
  }

  @Get('reports/ftd-analysis')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Tạo báo cáo phân tích FTD',
    description: 'Tạo báo cáo chi tiết về First Time Deposit với phân tích nguồn và conversion'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Báo cáo phân tích FTD',
    type: DataManagementReportDto
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async generateFTDAnalysisReport(@Query() query: DataManagementReportQueryDto): Promise<DataManagementReportDto> {
    return this.dataManagementService.generateReport({ ...query, report_type: 'ftd_analysis' });
  }

  @Get('reports/lifecycle')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Tạo báo cáo lifecycle khách hàng',
    description: 'Tạo báo cáo về vòng đời khách hàng từ đăng ký đến FTD và hoạt động dài hạn'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Báo cáo lifecycle khách hàng',
    type: DataManagementReportDto
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async generateLifecycleReport(@Query() query: DataManagementReportQueryDto): Promise<DataManagementReportDto> {
    return this.dataManagementService.generateReport({ ...query, report_type: 'lifecycle' });
  }

  // Additional Analytics Endpoints
  @Get('analytics/registration-overview')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy tổng quan đăng ký',
    description: 'Lấy thống kê tổng quan về đăng ký người dùng theo thời gian thực'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Thống kê tổng quan đăng ký',
    schema: {
      type: 'object',
      properties: {
        total_registrations: { type: 'number' },
        today_registrations: { type: 'number' },
        week_registrations: { type: 'number' },
        month_registrations: { type: 'number' },
        growth_rate: { type: 'number' },
        avg_acquisition_cost: { type: 'number' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  async getRegistrationOverview(@Query() query: DataManagementReportQueryDto) {
    const report = await this.dataManagementService.generateReport({ 
      ...query, 
      report_type: 'registration' 
    });
    return report.registration_stats;
  }

  @Get('analytics/member-overview')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy tổng quan thành viên',
    description: 'Lấy thống kê tổng quan về thành viên và hoạt động theo thời gian thực'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Thống kê tổng quan thành viên',
    schema: {
      type: 'object',
      properties: {
        total_members: { type: 'number' },
        new_members: { type: 'number' },
        active_members: { type: 'number' },
        at_risk_members: { type: 'number' },
        high_value_members: { type: 'number' },
        activity_rate: { type: 'number' },
        churn_rate: { type: 'number' },
        avg_lifetime_value: { type: 'number' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  async getMemberOverview(@Query() query: DataManagementReportQueryDto) {
    const report = await this.dataManagementService.generateReport({ 
      ...query, 
      report_type: 'member_analytics' 
    });
    return report.member_stats;
  }

  @Get('analytics/ftd-overview')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy tổng quan FTD',
    description: 'Lấy thống kê tổng quan về First Time Deposit theo thời gian thực'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Thống kê tổng quan FTD',
    schema: {
      type: 'object',
      properties: {
        total_ftd: { type: 'number' },
        total_ftd_amount: { type: 'number' },
        avg_ftd_amount: { type: 'number' },
        ftd_conversion_rate: { type: 'number' },
        avg_registration_to_ftd_days: { type: 'number' },
        today_ftd: { type: 'number' },
        week_ftd: { type: 'number' },
        month_ftd: { type: 'number' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  async getFTDOverview(@Query() query: DataManagementReportQueryDto) {
    const report = await this.dataManagementService.generateReport({ 
      ...query, 
      report_type: 'ftd_analysis' 
    });
    return report.ftd_stats;
  }

  @Get('analytics/dashboard-summary')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy tổng hợp dashboard',
    description: 'Lấy tất cả thống kê quan trọng cho dashboard quản lý dữ liệu'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Tổng hợp dashboard quản lý dữ liệu',
    schema: {
      type: 'object',
      properties: {
        registration_stats: { type: 'object' },
        member_stats: { type: 'object' },
        ftd_stats: { type: 'object' },
        generated_at: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  async getDashboardSummary(@Query() query: DataManagementReportQueryDto) {
    const report = await this.dataManagementService.generateReport({ 
      ...query, 
      report_type: 'comprehensive' 
    });
    
    return {
      registration_stats: report.registration_stats,
      member_stats: report.member_stats,
      ftd_stats: report.ftd_stats,
      generated_at: report.generated_at,
    };
  }
} 