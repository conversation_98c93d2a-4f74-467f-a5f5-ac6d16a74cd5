import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataManagementController } from './data-management.controller';
import { DataManagementService } from './data-management.service';
import { UserAnalytics } from '../../entities/user-analytics.entity';
import { User } from '../../entities/user.entity';
import { Department } from '../../entities/department.entity';
import { Deposit } from '../../entities/deposit.entity';
import { Bet } from '../../entities/bet.entity';
import { CommonServicesModule } from '../../common/services/common-services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserAnalytics,
      User,
      Department,
      Deposit,
      Bet
    ]),
    CommonServicesModule,
  ],
  controllers: [DataManagementController],
  providers: [DataManagementService],
  exports: [DataManagementService],
})
export class DataManagementModule {} 