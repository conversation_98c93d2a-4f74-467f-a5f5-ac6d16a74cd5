import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job, JobOptions } from 'bull';
import { CustomLoggerService } from '../logger/logger.service';
import { QUEUE_NAMES, JOB_TYPES, JOB_PRIORITIES, JOB_STATUS } from './queue.constants';

export interface QueueJobData {
  [key: string]: any;
}

export interface QueueJobOptions extends JobOptions {
  priority?: number;
  delay?: number;
  attempts?: number;
  backoff?: string | { type: string; delay: number };
  removeOnComplete?: number | boolean;
  removeOnFail?: number | boolean;
}

export interface JobInfo {
  id: string | number;
  name: string;
  data: any;
  opts: any;
  progress: number;
  delay: number;
  timestamp: number;
  attemptsMade: number;
  failedReason?: string;
  stacktrace?: string[];
  returnvalue?: any;
  finishedOn?: number;
  processedOn?: number;
}

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue(QUEUE_NAMES.IMPORT_EXPORT) private importExportQueue: Queue,
    @InjectQueue(QUEUE_NAMES.FILE_PROCESSING) private fileProcessingQueue: Queue,
    @InjectQueue(QUEUE_NAMES.EMAIL_NOTIFICATIONS) private emailQueue: Queue,
    @InjectQueue(QUEUE_NAMES.DATA_ANALYTICS) private analyticsQueue: Queue,
    private readonly logger: CustomLoggerService,
  ) {}

  /**
   * Add a job to the import/export queue
   */
  async addImportExportJob(
    jobType: string,
    data: QueueJobData,
    options: QueueJobOptions = {},
  ): Promise<Job> {
    try {
      const defaultOptions: QueueJobOptions = {
        priority: JOB_PRIORITIES.NORMAL,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      };

      const jobOptions = { ...defaultOptions, ...options };
      const job = await this.importExportQueue.add(jobType, data, jobOptions);

      this.logger.info(
        `Added import/export job: ${jobType} with ID: ${job.id}`,
        'QueueService',
        { jobType, jobId: job.id, data }
      );

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add import/export job: ${jobType}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Add a job to the file processing queue
   */
  async addFileProcessingJob(
    jobType: string,
    data: QueueJobData,
    options: QueueJobOptions = {},
  ): Promise<Job> {
    try {
      const defaultOptions: QueueJobOptions = {
        priority: JOB_PRIORITIES.HIGH,
        attempts: 2,
        removeOnComplete: 5,
        removeOnFail: 20,
      };

      const jobOptions = { ...defaultOptions, ...options };
      const job = await this.fileProcessingQueue.add(jobType, data, jobOptions);

      this.logger.info(
        `Added file processing job: ${jobType} with ID: ${job.id}`,
        'QueueService',
        { jobType, jobId: job.id, data }
      );

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add file processing job: ${jobType}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Add a job to the email queue
   */
  async addEmailJob(
    jobType: string,
    data: QueueJobData,
    options: QueueJobOptions = {},
  ): Promise<Job> {
    try {
      const defaultOptions: QueueJobOptions = {
        priority: JOB_PRIORITIES.NORMAL,
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
        removeOnComplete: 20,
        removeOnFail: 100,
      };

      const jobOptions = { ...defaultOptions, ...options };
      const job = await this.emailQueue.add(jobType, data, jobOptions);

      this.logger.info(
        `Added email job: ${jobType} with ID: ${job.id}`,
        'QueueService',
        { jobType, jobId: job.id }
      );

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add email job: ${jobType}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Add a job to the analytics queue
   */
  async addAnalyticsJob(
    jobType: string,
    data: QueueJobData,
    options: QueueJobOptions = {},
  ): Promise<Job> {
    try {
      const defaultOptions: QueueJobOptions = {
        priority: JOB_PRIORITIES.LOW,
        attempts: 2,
        removeOnComplete: 5,
        removeOnFail: 10,
      };

      const jobOptions = { ...defaultOptions, ...options };
      const job = await this.analyticsQueue.add(jobType, data, jobOptions);

      this.logger.info(
        `Added analytics job: ${jobType} with ID: ${job.id}`,
        'QueueService',
        { jobType, jobId: job.id, data }
      );

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add analytics job: ${jobType}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Get job by ID from any queue
   */
  async getJob(queueName: string, jobId: string | number): Promise<Job | null> {
    try {
      const queue = this.getQueueByName(queueName);
      const job = await queue.getJob(jobId);
      return job;
    } catch (error) {
      this.logger.error(
        `Failed to get job ${jobId} from queue ${queueName}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(queueName: string) {
    try {
      const queue = this.getQueueByName(queueName);
      const [waiting, active, completed, failed, delayed, paused] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
        queue.getPaused(),
      ]);

      return {
        name: queueName,
        counts: {
          waiting: waiting.length,
          active: active.length,
          completed: completed.length,
          failed: failed.length,
          delayed: delayed.length,
          paused: paused.length,
        },
        jobs: {
          waiting: waiting.map(job => this.formatJobInfo(job)),
          active: active.map(job => this.formatJobInfo(job)),
          completed: completed.slice(0, 10).map(job => this.formatJobInfo(job)),
          failed: failed.slice(0, 10).map(job => this.formatJobInfo(job)),
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get stats for queue ${queueName}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Get all queues statistics
   */
  async getAllQueuesStats() {
    try {
      const queueNames = Object.values(QUEUE_NAMES);
      const stats = await Promise.all(
        queueNames.map(queueName => this.getQueueStats(queueName))
      );

      return {
        queues: stats,
        summary: {
          totalQueues: stats.length,
          totalJobs: stats.reduce((sum, queue) =>
            sum + Object.values(queue.counts).reduce((a, b) => a + b, 0), 0
          ),
          totalWaiting: stats.reduce((sum, queue) => sum + queue.counts.waiting, 0),
          totalActive: stats.reduce((sum, queue) => sum + queue.counts.active, 0),
          totalCompleted: stats.reduce((sum, queue) => sum + queue.counts.completed, 0),
          totalFailed: stats.reduce((sum, queue) => sum + queue.counts.failed, 0),
        },
      };
    } catch (error) {
      this.logger.error(
        'Failed to get all queues stats',
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Remove job by ID
   */
  async removeJob(queueName: string, jobId: string | number): Promise<void> {
    try {
      const job = await this.getJob(queueName, jobId);
      if (job) {
        await job.remove();
        this.logger.info(
          `Removed job ${jobId} from queue ${queueName}`,
          'QueueService'
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to remove job ${jobId} from queue ${queueName}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Retry failed job
   */
  async retryJob(queueName: string, jobId: string | number): Promise<void> {
    try {
      const job = await this.getJob(queueName, jobId);
      if (job) {
        await job.retry();
        this.logger.info(
          `Retried job ${jobId} in queue ${queueName}`,
          'QueueService'
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to retry job ${jobId} in queue ${queueName}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Clean queue (remove completed/failed jobs)
   */
  async cleanQueue(queueName: string, grace: number = 0, limit: number = 100): Promise<void> {
    try {
      const queue = this.getQueueByName(queueName);
      await queue.clean(grace, 'completed', limit);
      await queue.clean(grace, 'failed', limit);

      this.logger.info(
        `Cleaned queue ${queueName} (grace: ${grace}ms, limit: ${limit})`,
        'QueueService'
      );
    } catch (error) {
      this.logger.error(
        `Failed to clean queue ${queueName}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Pause queue
   */
  async pauseQueue(queueName: string): Promise<void> {
    try {
      const queue = this.getQueueByName(queueName);
      await queue.pause();
      this.logger.info(`Paused queue ${queueName}`, 'QueueService');
    } catch (error) {
      this.logger.error(
        `Failed to pause queue ${queueName}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Resume queue
   */
  async resumeQueue(queueName: string): Promise<void> {
    try {
      const queue = this.getQueueByName(queueName);
      await queue.resume();
      this.logger.info(`Resumed queue ${queueName}`, 'QueueService');
    } catch (error) {
      this.logger.error(
        `Failed to resume queue ${queueName}`,
        error.stack,
        'QueueService'
      );
      throw error;
    }
  }

  /**
   * Get queue by name
   */
  private getQueueByName(queueName: string): Queue {
    switch (queueName) {
      case QUEUE_NAMES.IMPORT_EXPORT:
        return this.importExportQueue;
      case QUEUE_NAMES.FILE_PROCESSING:
        return this.fileProcessingQueue;
      case QUEUE_NAMES.EMAIL_NOTIFICATIONS:
        return this.emailQueue;
      case QUEUE_NAMES.DATA_ANALYTICS:
        return this.analyticsQueue;
      default:
        throw new Error(`Unknown queue name: ${queueName}`);
    }
  }

  /**
   * Format job info for API response
   */
  private formatJobInfo(job: Job): JobInfo {
    return {
      id: job.id,
      name: job.name,
      data: job.data,
      opts: job.opts,
      progress: job.progress(),
      delay: job.delay,
      timestamp: job.timestamp,
      attemptsMade: job.attemptsMade,
      failedReason: job.failedReason,
      stacktrace: job.stacktrace,
      returnvalue: job.returnvalue,
      finishedOn: job.finishedOn,
      processedOn: job.processedOn,
    };
  }
}
