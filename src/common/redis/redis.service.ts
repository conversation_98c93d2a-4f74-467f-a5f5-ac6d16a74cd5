import { Injectable, Inject, OnModuleD<PERSON>roy } from '@nestjs/common';
import { Redis } from 'ioredis';
import { CustomLoggerService } from '../logger/logger.service';

@Injectable()
export class RedisService implements OnModuleDestroy {
  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: Redis,
    private readonly logger: CustomLoggerService,
  ) {}

  async onModuleDestroy() {
    await this.redisClient.quit();
  }

  /**
   * Get Redis client instance
   */
  getClient(): Redis {
    return this.redisClient;
  }

  /**
   * Set a key-value pair with optional expiration
   */
  async set(key: string, value: string | number | object, ttl?: number): Promise<void> {
    try {
      const serializedValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
      
      if (ttl) {
        await this.redisClient.setex(key, ttl, serializedValue);
      } else {
        await this.redisClient.set(key, serializedValue);
      }

      this.logger.debug(`Redis SET: ${key}`, 'RedisService');
    } catch (error) {
      this.logger.error(`Redis SET error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Get value by key
   */
  async get(key: string): Promise<string | null> {
    try {
      const value = await this.redisClient.get(key);
      this.logger.debug(`Redis GET: ${key}`, 'RedisService');
      return value;
    } catch (error) {
      this.logger.error(`Redis GET error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Get and parse JSON value
   */
  async getJson<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error(`Redis GET JSON error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Delete a key
   */
  async del(key: string): Promise<number> {
    try {
      const result = await this.redisClient.del(key);
      this.logger.debug(`Redis DEL: ${key}`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis DEL error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redisClient.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`Redis EXISTS error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Set expiration for a key
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await this.redisClient.expire(key, seconds);
      return result === 1;
    } catch (error) {
      this.logger.error(`Redis EXPIRE error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Get keys by pattern
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      const keys = await this.redisClient.keys(pattern);
      this.logger.debug(`Redis KEYS: ${pattern} (found ${keys.length})`, 'RedisService');
      return keys;
    } catch (error) {
      this.logger.error(`Redis KEYS error for pattern ${pattern}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Increment a numeric value
   */
  async incr(key: string): Promise<number> {
    try {
      const result = await this.redisClient.incr(key);
      this.logger.debug(`Redis INCR: ${key} = ${result}`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis INCR error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Decrement a numeric value
   */
  async decr(key: string): Promise<number> {
    try {
      const result = await this.redisClient.decr(key);
      this.logger.debug(`Redis DECR: ${key} = ${result}`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis DECR error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Hash operations
   */
  async hset(key: string, field: string, value: string | number): Promise<number> {
    try {
      const result = await this.redisClient.hset(key, field, String(value));
      this.logger.debug(`Redis HSET: ${key}.${field}`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis HSET error for ${key}.${field}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  async hget(key: string, field: string): Promise<string | null> {
    try {
      const result = await this.redisClient.hget(key, field);
      this.logger.debug(`Redis HGET: ${key}.${field}`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis HGET error for ${key}.${field}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    try {
      const result = await this.redisClient.hgetall(key);
      this.logger.debug(`Redis HGETALL: ${key}`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis HGETALL error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * List operations
   */
  async lpush(key: string, ...values: string[]): Promise<number> {
    try {
      const result = await this.redisClient.lpush(key, ...values);
      this.logger.debug(`Redis LPUSH: ${key} (${values.length} items)`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis LPUSH error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  async rpop(key: string): Promise<string | null> {
    try {
      const result = await this.redisClient.rpop(key);
      this.logger.debug(`Redis RPOP: ${key}`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis RPOP error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    try {
      const result = await this.redisClient.lrange(key, start, stop);
      this.logger.debug(`Redis LRANGE: ${key} [${start}:${stop}]`, 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis LRANGE error for key ${key}: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Flush database (use with caution)
   */
  async flushdb(): Promise<void> {
    try {
      await this.redisClient.flushdb();
      this.logger.warn('Redis database flushed', 'RedisService');
    } catch (error) {
      this.logger.error(`Redis FLUSHDB error: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Get Redis info
   */
  async info(section?: string): Promise<string> {
    try {
      const result = await this.redisClient.info(section);
      return result;
    } catch (error) {
      this.logger.error(`Redis INFO error: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }

  /**
   * Ping Redis server
   */
  async ping(): Promise<string> {
    try {
      const result = await this.redisClient.ping();
      this.logger.debug('Redis PING successful', 'RedisService');
      return result;
    } catch (error) {
      this.logger.error(`Redis PING error: ${error.message}`, error.stack, 'RedisService');
      throw error;
    }
  }
}
