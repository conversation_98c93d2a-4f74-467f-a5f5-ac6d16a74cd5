# HỆ THỐNG CMS NH88 - TỔNG QUAN & HƯỚNG DẪN SỬ DỤNG

## 1. TỔNG QUAN HỆ THỐNG

### 1.1 G<PERSON>ớ<PERSON> thiệu

Hệ thống CMS NH88 là một Content Management System được phát triển bằng NestJS, hỗ trợ quản lý toàn diện cho các hoạt động kinh doanh trực tuyến bao gồm:

- Qu<PERSON>n lý người dùng và phân quyền
- Quản lý affiliate marketing
- <PERSON> dõi cá cược và giao dịch
- Phân tích dữ liệu và báo cáo
- Quản lý chi phí quảng cáo

### 1.2 Kiến trúc hệ thống

- **Backend Framework**: NestJS với TypeScript
- **Database**: TypeORM (PostgreSQL)
- **Authentication**: JWT + Local Strategy
- **File Upload**: Multer
- **Logging**: Custom Logger Service
- **Audit Trail**: Comprehensive audit logging

---

## 2. CÁC ENTITY CHÍNH

### 2.1 User Entity (`users`)

**Mô tả**: <PERSON><PERSON><PERSON><PERSON> lý thông tin người dùng hệ thống

**Các trường chính**:

- `username` (string, unique): Tên đăng nhập duy nhất
- `fullName` (string): Họ và tên đầy đủ
- `email` (string, unique): Email đăng nhập
- `phone` (string, nullable): Số điện thoại
- `password` (string): Mật khẩu đã mã hóa
- `address`, `city`, `state`, `zip`, `country`: Thông tin địa chỉ

**Relationships**:

- `OneToMany` với `UserRoleAssignment`: Phân quyền người dùng
- `OneToMany` với `Affiliate`: Mã affiliate của người dùng

### 2.2 Role Entity (`roles`)

**Mô tả**: Quản lý vai trò và cấp độ quyền hạn

**Các trường chính**:

- `name` (RoleType): Tên vai trò (enum)
- `displayName` (string): Tên hiển thị
- `level` (number): Cấp độ quyền (1=cao nhất, 4=thấp nhất)
- `description` (text): Mô tả vai trò
- `isSystem` (boolean): Vai trò hệ thống (không thể xóa)

**Relationships**:

- `OneToMany` với `UserRoleAssignment`: Gán vai trò cho người dùng
- `OneToMany` với `RolePermissionAssignment`: Gán quyền cho vai trò

### 2.3 Permission Entity (`permissions`)

**Mô tả**: Quản lý quyền hạn chi tiết

**Các trường chính**:

- `action` (PermissionsType): Tên hành động (enum)
- `description` (text): Mô tả quyền hạn

**Relationships**:

- `OneToMany` với `RolePermissionAssignment`: Gán quyền cho vai trò
- `OneToMany` với `PermissionGroupDetail`: Nhóm quyền

### 2.4 Affiliate Entity (`affiliates`)

**Mô tả**: Quản lý mã affiliate và tracking

**Các trường chính**:

- `code` (string, unique): Mã affiliate duy nhất
- `userId` (uuid): ID người dùng sở hữu
- `description` (string): Mô tả mã affiliate
- `isActive` (boolean): Trạng thái kích hoạt
- `activatedAt`, `deactivatedAt`: Thời gian kích hoạt/vô hiệu hóa
- `totalUsageCount` (number): Tổng số lần sử dụng
- `successfulReferrals` (number): Số lần giới thiệu thành công
- `commissionRate` (decimal): Tỷ lệ hoa hồng
- `totalCommissionEarned` (decimal): Tổng hoa hồng đã kiếm
- `expiresAt` (timestamp): Thời gian hết hạn
- `maxUsageLimit` (number): Giới hạn sử dụng tối đa
- `category` (string): Loại mã affiliate

**Virtual Properties**:

- `isExpired`: Kiểm tra hết hạn
- `isUsageLimitReached`: Kiểm tra đã đạt giới hạn
- `canBeUsed`: Kiểm tra có thể sử dụng
- `conversionRate`: Tỷ lệ chuyển đổi

### 2.5 Bet Entity (`bets`)

**Mô tả**: Quản lý thông tin cá cược

**Các trường chính**:

- `bet_amount` (decimal): Số tiền cược
- `win_amount` (decimal): Số tiền thắng
- `loss_amount` (decimal): Số tiền thua
- `bet_id` (string): ID cược
- `game_type` (string): Loại game
- `game_name` (string): Tên game
- `status` (BetStatus): Trạng thái cược
- `settled_at` (timestamp): Thời gian settle
- `notes` (text): Ghi chú
- `external_reference` (string): Mã tham chiếu ngoài

**Relationships**:

- `ManyToOne` với `Department`: Phòng ban quản lý

### 2.6 Deposit Entity (`deposits`)

**Mô tả**: Quản lý giao dịch nạp tiền

**Các trường chính**:

- `amount` (decimal): Số tiền nạp
- `transaction_id` (string): ID giao dịch
- `payment_method` (string): Phương thức thanh toán
- `status` (DepositStatus): Trạng thái giao dịch
- `processed_at` (timestamp): Thời gian xử lý
- `notes` (text): Ghi chú
- `external_reference` (string): Mã tham chiếu ngoài

**Relationships**:

- `ManyToOne` với `Department`: Phòng ban quản lý
- `ManyToOne` với `User`: Người dùng nạp tiền

---

## 3. CÁC MODULE CHÍNH

### 3.1 Authentication Module (`auth`)

**Mục đích**: Xác thực và phân quyền người dùng

**Tính năng chính**:

- Đăng nhập với username/email và password
- JWT token authentication
- Local strategy authentication
- Bảo mật session

**Cách sử dụng**:

```http
POST /auth/login
{
  "username": "admin",
  "password": "password123"
}

POST /auth/register
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "Full Name"
}
```

### 3.2 User Management Module (`users`)

**Mục đích**: Quản lý người dùng hệ thống

**Tính năng chính**:

- CRUD operations cho user
- Quản lý profile người dùng
- Tích hợp với affiliate system

**Cách sử dụng**:

```http
GET /users - Danh sách người dùng
POST /users - Tạo người dùng mới
PUT /users/:id - Cập nhật người dùng
DELETE /users/:id - Xóa người dùng
```

### 3.3 Role & Permission Management

**Modules liên quan**: `roles`, `permissions`, `role-permission-assignments`, `user-role-assignments`

**Tính năng chính**:

- Quản lý vai trò hệ thống
- Gán quyền cho vai trò
- Gán vai trò cho người dùng
- Kiểm tra quyền hạn

**Cách sử dụng**:

```http
GET /roles - Danh sách vai trò
POST /role-permission-assignments - Gán quyền cho vai trò
POST /user-role-assignments - Gán vai trò cho user
```

### 3.4 Affiliate Management Module (`affiliate`)

**Mục đích**: Quản lý chương trình affiliate marketing

**Tính năng chính**:

- Tạo và quản lý mã affiliate
- Theo dõi hiệu suất affiliate
- Tính toán hoa hồng
- Báo cáo affiliate

**Cách sử dụng**:

```http
GET /affiliate - Danh sách affiliate
POST /affiliate - Tạo mã affiliate mới
PUT /affiliate/:id - Cập nhật mã affiliate
GET /affiliate/:id/reports - Báo cáo hiệu suất
```

### 3.5 Betting Management Module (`bets`)

**Mục đích**: Quản lý hoạt động cá cược

**Tính năng chính**:

- Theo dõi các cược
- Quản lý trạng thái cược
- Báo cáo cược theo thời gian
- Phân tích game performance

**Cách sử dụng**:

```http
GET /bets - Danh sách cược
POST /bets - Tạo cược mới
PUT /bets/:id - Cập nhật trạng thái cược
GET /bets/charts - Biểu đồ cược
```

### 3.6 Deposit Management Module (`deposits`)

**Mục đích**: Quản lý giao dịch nạp tiền

**Tính năng chính**:

- Theo dõi giao dịch nạp tiền
- Quản lý trạng thái giao dịch
- First Time Deposit (FTD) reports
- Phân tích deposit trends

**Cách sử dụng**:

```http
GET /deposits - Danh sách giao dịch
POST /deposits - Tạo giao dịch mới
PUT /deposits/:id - Cập nhật trạng thái
GET /deposits/ftd-reports - Báo cáo FTD
```

### 3.7 Dashboard Module (`dashboard`)

**Mục đích**: Tổng quan và báo cáo tổng hợp

**Tính năng chính**:

- Biểu đồ tổng quan
- KPI dashboard
- Real-time analytics
- Custom charts

**Cách sử dụng**:

```http
GET /dashboard/charts - Biểu đồ tổng quan
GET /dashboard/kpi - Chỉ số KPI
GET /dashboard/real-time - Dữ liệu real-time
```

### 3.8 Cost Management Module (`costs`)

**Mục đích**: Quản lý chi phí quảng cáo và marketing

**Tính năng chính**:

- Theo dõi chi phí quảng cáo
- Phân tích ROI
- Báo cáo chi phí theo campaign
- Budget management

**Cách sử dụng**:

```http
GET /costs - Danh sách chi phí
POST /costs - Thêm chi phí mới
GET /costs/reports - Báo cáo chi phí
```

### 3.9 Ad Performance Module (`ad-performance`)

**Mục đích**: Theo dõi hiệu suất quảng cáo

**Tính năng chính**:

- Tracking ad performance
- Conversion tracking
- A/B testing results
- Campaign optimization

**Cách sử dụng**:

```http
GET /ad-performance - Dữ liệu hiệu suất
POST /ad-performance - Thêm dữ liệu tracking
GET /ad-performance/reports - Báo cáo hiệu suất
```

### 3.10 Data Management Module (`data-management`)

**Mục đích**: Quản lý và phân tích dữ liệu người dùng

**Tính năng chính**:

- User analytics tracking
- Behavior analysis
- Segmentation
- Data export/import

**Cách sử dụng**:

```http
GET /data-management - Dữ liệu analytics
POST /data-management - Thêm dữ liệu tracking
GET /data-management/reports - Báo cáo phân tích
```

### 3.11 Organization Management

**Modules liên quan**: `departments`, `teams`

**Tính năng chính**:

- Quản lý phòng ban
- Quản lý team
- Organizational structure
- Hierarchy management

**Cách sử dụng**:

```http
GET /departments - Danh sách phòng ban
POST /departments - Tạo phòng ban mới
GET /teams - Danh sách team
POST /teams - Tạo team mới
```

### 3.12 Audit & Logging Module (`audit`)

**Mục đích**: Theo dõi hoạt động hệ thống

**Tính năng chính**:

- Audit trail cho mọi thao tác
- User activity logging
- System change tracking
- Security monitoring

**Cách sử dụng**:

```http
GET /audit/logs - Danh sách audit logs
GET /audit/user-activities - Hoạt động người dùng
GET /audit/system-changes - Thay đổi hệ thống
```

### 3.13 Upload Module (`upload`)

**Mục đích**: Quản lý file upload

**Tính năng chính**:

- File upload với validation
- Image processing
- Document management
- Temporary file handling

**Cách sử dụng**:

```http
POST /upload/image - Upload hình ảnh
POST /upload/document - Upload tài liệu
DELETE /upload/:id - Xóa file
```

---

## 4. BẢO MẬT & PHÂN QUYỀN

### 4.1 Authentication Strategy

- **JWT Token**: Sử dụng JWT cho stateless authentication
- **Local Strategy**: Username/password authentication
- **Token Expiration**: Configurable token expiration
- **Refresh Token**: Support for token refresh

### 4.2 Authorization System

- **Role-Based Access Control (RBAC)**: Phân quyền dựa trên vai trò
- **Permission-Based**: Kiểm tra quyền hạn chi tiết
- **Hierarchical Roles**: Vai trò có cấp độ (1-4)
- **Guards**: `JwtAuthGuard`, `RolesGuard`, `PermissionsGuard`

### 4.3 Security Features

- **Audit Logging**: Ghi log mọi thao tác quan trọng
- **User Context**: Tracking user context cho mọi request
- **Input Validation**: Comprehensive DTO validation
- **Rate Limiting**: Configurable rate limiting

---

## 5. MONITORING & ANALYTICS

### 5.1 System Monitoring

- **Custom Logger**: Multi-level logging system
- **HTTP Logging**: Request/response logging
- **Error Tracking**: Comprehensive error handling
- **Performance Metrics**: System performance monitoring

### 5.2 Business Analytics

- **User Behavior**: Tracking user interactions
- **Conversion Tracking**: Affiliate conversion analysis
- **Financial Metrics**: Revenue and cost analysis
- **Campaign Performance**: Marketing campaign tracking

---

## 6. CÁCH SỬ DỤNG CHUNG

### 6.1 Authentication Flow

1. **Login**: `POST /auth/login` để lấy JWT token
2. **Authorization**: Include token trong header: `Authorization: Bearer <token>`
3. **Protected Routes**: Tất cả API cần authentication trừ login/register

### 6.2 Error Handling

- **Standard HTTP Status Codes**: 200, 201, 400, 401, 403, 404, 500
- **Error Response Format**:

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request"
}
```

### 6.3 Pagination

- **Query Parameters**: `page`, `limit`, `search`, `sort`
- **Response Format**:

```json
{
  "data": [...],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

### 6.4 Search & Filtering

- **Global Search**: Tìm kiếm trong multiple fields
- **Date Range**: Filtering theo khoảng thời gian
- **Status Filter**: Filtering theo trạng thái
- **Advanced Filters**: Custom filters cho từng module

---

## 7. DEPLOYMENT & CONFIGURATION

### 7.1 Environment Variables

```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=nh88_cms

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRATION=3600

# Upload
UPLOAD_DESTINATION=./uploads
MAX_FILE_SIZE=10485760
```

### 7.2 Scripts

- `yarn start`: Start production server
- `yarn start:dev`: Start development server
- `yarn build`: Build for production
- `yarn test`: Run tests
- `first_run.sh`: Initial setup script

---

## 8. API DOCUMENTATION

### 8.1 Common Headers

```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### 8.2 Response Formats

- **Success Response**: Status 200/201 với data
- **Error Response**: Status 4xx/5xx với error message
- **Validation Error**: Detailed field validation errors

### 8.3 Example Files

- `examples/affiliate-reports-examples.http`: Affiliate API examples
- `examples/dashboard-charts-examples.http`: Dashboard API examples
- `examples/ftd-reports-examples.http`: FTD reports examples

---

## 9. BEST PRACTICES

### 9.1 Development Guidelines

- **TypeScript**: Strict typing cho tất cả code
- **DTOs**: Input/output validation với class-validator
- **Services**: Business logic trong services
- **Controllers**: Thin controllers, delegate to services

### 9.2 Database Guidelines

- **Migrations**: Sử dụng TypeORM migrations
- **Relationships**: Properly defined entity relationships
- **Indexes**: Optimize với database indexes
- **Audit Trail**: Comprehensive audit logging

### 9.3 Security Guidelines

- **Input Sanitization**: Validate tất cả user input
- **SQL Injection Prevention**: Sử dụng parameterized queries
- **XSS Prevention**: Proper output encoding
- **CSRF Protection**: Implement CSRF tokens

---

## 10. TROUBLESHOOTING

### 10.1 Common Issues

- **Authentication Failed**: Kiểm tra JWT token và expiration
- **Permission Denied**: Kiểm tra user roles và permissions
- **Database Connection**: Verify database credentials
- **File Upload Issues**: Check file size limits và permissions

### 10.2 Debugging

- **Logs**: Check application logs trong console
- **Database**: Verify data consistency
- **Network**: Check API requests/responses
- **Performance**: Monitor response times

---

## 11. SUPPORT & MAINTENANCE

### 11.1 Regular Maintenance

- **Database Cleanup**: Clean up old audit logs
- **File Cleanup**: Remove temporary files
- **Security Updates**: Regular dependency updates
- **Performance Optimization**: Monitor and optimize queries

### 11.2 Backup Strategy

- **Database Backup**: Regular automated backups
- **File Backup**: Backup upload directories
- **Configuration Backup**: Version control cho configs
- **Recovery Plan**: Documented recovery procedures

---

_Tài liệu này cung cấp tổng quan chi tiết về hệ thống CMS NH88. Để biết thêm chi tiết về từng module cụ thể, vui lòng tham khảo source code và API documentation._
